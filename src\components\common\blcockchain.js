import React, { useState, useEffect } from 'react';
import { Search, Activity, Hash, Clock, Zap, Users, TrendingUp, Server, Globe } from 'lucide-react';

function GanacheExplorer() {
    // États principaux
    const [latestBlocks, setLatestBlocks] = useState([]);
    const [blockDetails, setBlockDetails] = useState(null);
    const [transactionDetails, setTransactionDetails] = useState(null);
    const [searchInput, setSearchInput] = useState('');
    const [ganacheInfo, setGanacheInfo] = useState({
        chainId: 1337,
        networkType: 'Ganache Local',
        rpcUrl: 'http://127.0.0.1:7545',
        gasPrice: '20.0',
        blockHeight: 156,
        totalTransactions: 342,
        activeAccounts: 10,
        networkStatus: 'Connecté'
    });

    // Initialisation de Web3 avec Ganache
    useEffect(() => {
        const initWeb3 = async () => {
            try {
                setLoading(true);
                setConnectionError('');

                // Importer Web3 dynamiquement
                const Web3 = (await import('https://cdnjs.cloudflare.com/ajax/libs/web3/1.10.0/web3.min.js')).default;

                // Tentative de connexion à Ganache
                const ganacheUrl = 'http://localhost:7545';
                const web3Instance = new Web3(ganacheUrl);

                // Test de connexion
                const blockNumber = await web3Instance.eth.getBlockNumber();
                console.log('Connecté à Ganache, bloc actuel:', blockNumber);

                setWeb3(web3Instance);
                setIsConnected(true);

                // Récupérer toutes les informations
                await Promise.all([
                    fetchNetworkInfo(web3Instance),
                    fetchLatestBlocks(web3Instance),
                    fetchAccounts(web3Instance),
                    fetchBlockchainStats(web3Instance)
                ]);

            } catch (error) {
                console.error("Erreur de connexion à Ganache:", error);
                setConnectionError(`Impossible de se connecter à Ganache. Assurez-vous que Ganache est lancé sur http://localhost:7545`);
                setIsConnected(false);

                // Fallback vers des données simulées
                initMockData();
            } finally {
                setLoading(false);
            }
        };

        initWeb3();

        // Actualisation périodique si connecté
        const interval = setInterval(() => {
            if (web3 && isConnected) {
                fetchLatestBlocks(web3);
                fetchNetworkInfo(web3);
                fetchBlockchainStats(web3);
            }
        }, 3000); // Actualise toutes les 3 secondes

        return () => clearInterval(interval);
    }, []);

    // Récupérer les informations réseau étendues
    const fetchNetworkInfo = async (web3Instance) => {
        try {
            const [chainId, networkType, gasPrice, blockNumber, isListening] = await Promise.all([
                web3Instance.eth.getChainId(),
                web3Instance.eth.net.getNetworkType(),
                web3Instance.eth.getGasPrice(),
                web3Instance.eth.getBlockNumber(),
                web3Instance.eth.net.isListening()
            ]);

            // Récupérer le dernier bloc pour plus d'infos
            const latestBlock = await web3Instance.eth.getBlock(blockNumber);

            setNetworkInfo({
                chainId: chainId.toString(),
                networkType: networkType || 'development',
                gasPrice: web3Instance.utils.fromWei(gasPrice.toString(), 'gwei'),
                blockHeight: blockNumber,
                peerCount: 0, // Ganache n'a pas de peers
                isListening,
                difficulty: latestBlock.difficulty || 0,
                totalDifficulty: latestBlock.totalDifficulty || 0,
                hashrate: 0 // Calculé approximativement
            });

            // Calculer le temps depuis le dernier bloc
            if (latestBlock) {
                const now = Math.floor(Date.now() / 1000);
                setLastBlockTime(now - latestBlock.timestamp);
            }
        } catch (error) {
            console.error("Erreur lors de la récupération des infos réseau:", error);
        }
    };

    // Récupérer les statistiques de la blockchain
    const fetchBlockchainStats = async (web3Instance) => {
        try {
            const latestBlockNumber = await web3Instance.eth.getBlockNumber();
            let totalTransactions = 0;
            let totalGasUsed = 0;
            let totalBlockSize = 0;
            const blockTimesTemp = [];

            // Analyser les 20 derniers blocs pour les statistiques
            const blocksToAnalyze = Math.min(20, latestBlockNumber + 1);
            const blocks = [];

            for (let i = 0; i < blocksToAnalyze; i++) {
                const blockNum = latestBlockNumber - i;
                if (blockNum >= 0) {
                    const block = await web3Instance.eth.getBlock(blockNum, true);
                    if (block) {
                        blocks.push(block);
                        totalTransactions += block.transactions.length;
                        totalGasUsed += block.gasUsed || 0;
                        totalBlockSize += block.size || 0;

                        // Calculer les temps entre blocs
                        if (i > 0 && blocks[i-1]) {
                            const timeDiff = blocks[i-1].timestamp - block.timestamp;
                            blockTimesTemp.push(timeDiff);
                        }
                    }
                }
            }

            // Calculer la moyenne des temps de bloc
            const avgBlockTime = blockTimesTemp.length > 0 ?
                blockTimesTemp.reduce((a, b) => a + b, 0) / blockTimesTemp.length : 15;

            // Récupérer le supply total des comptes
            const accountList = await web3Instance.eth.getAccounts();
            let totalSupply = 0;

            for (const account of accountList.slice(0, 10)) {
                const balance = await web3Instance.eth.getBalance(account);
                totalSupply += parseFloat(web3Instance.utils.fromWei(balance, 'ether'));
            }

            setBlockchainStats({
                totalTransactions,
                avgBlockTime: Math.round(avgBlockTime),
                totalAccounts: accountList.length,
                totalSupply: totalSupply.toFixed(2),
                networkHashrate: Math.round(Math.random() * 1000000), // Simulé pour Ganache
                avgGasPrice: parseFloat(web3Instance.utils.fromWei((await web3Instance.eth.getGasPrice()).toString(), 'gwei')),
                blockSize: Math.round(totalBlockSize / blocksToAnalyze) || 500
            });

            setBlockTimes(blockTimesTemp);
        } catch (error) {
            console.error("Erreur lors du calcul des statistiques:", error);
        }
    };

    // Récupérer les derniers blocs avec plus de détails
    const fetchLatestBlocks = async (web3Instance) => {
        try {
            const latestBlockNumber = await web3Instance.eth.getBlockNumber();
            const blockPromises = [];
            const transactionPromises = [];

            // Récupérer les 15 derniers blocs
            const numberOfBlocks = Math.min(15, latestBlockNumber + 1);
            for (let i = 0; i < numberOfBlocks; i++) {
                const blockNum = latestBlockNumber - i;
                if (blockNum >= 0) {
                    blockPromises.push(
                        web3Instance.eth.getBlock(blockNum, true)
                    );
                }
            }

            const blocks = await Promise.all(blockPromises);
            const validBlocks = blocks.filter(block => block !== null);
            setLatestBlocks(validBlocks);

            // Extraire les transactions récentes des derniers blocs
            const recentTxs = [];
            validBlocks.slice(0, 5).forEach(block => {
                if (block.transactions && block.transactions.length > 0) {
                    block.transactions.slice(0, 3).forEach(tx => {
                        recentTxs.push({
                            ...tx,
                            blockNumber: block.number,
                            timestamp: block.timestamp
                        });
                    });
                }
            });

            setRecentTransactions(recentTxs.slice(0, 10));
        } catch (error) {
            console.error("Erreur lors de la récupération des blocs:", error);
        }
    };

    // Récupérer les comptes avec plus de détails
    const fetchAccounts = async (web3Instance) => {
        try {
            const accountList = await web3Instance.eth.getAccounts();
            const accountsWithDetails = await Promise.all(
                accountList.slice(0, 8).map(async (account, index) => {
                    const balance = await web3Instance.eth.getBalance(account);
                    const transactionCount = await web3Instance.eth.getTransactionCount(account);

                    return {
                        address: account,
                        balance: web3Instance.utils.fromWei(balance, 'ether'),
                        transactionCount,
                        index: index
                    };
                })
            );
            setAccounts(accountsWithDetails);
        } catch (error) {
            console.error("Erreur lors de la récupération des comptes:", error);
        }
    };

    // Données simulées enrichies
    const initMockData = () => {
        const blocks = [];
        const baseBlockNumber = 150;

        for (let i = 0; i < 15; i++) {
            const blockNumber = baseBlockNumber - i;
            const txCount = Math.floor(Math.random() * 8);
            const timestamp = Math.floor(Date.now() / 1000) - (i * 12);

            blocks.push({
                number: blockNumber,
                hash: `0x${Math.random().toString(16).substr(2, 64)}`,
                transactions: new Array(txCount).fill(null).map(() => ({
                    hash: `0x${Math.random().toString(16).substr(2, 64)}`,
                    from: `0x${Math.random().toString(16).substr(2, 40)}`,
                    to: `0x${Math.random().toString(16).substr(2, 40)}`,
                    value: (Math.random() * 10).toString()
                })),
                gasUsed: Math.floor(Math.random() * 2000000),
                gasLimit: 6721975,
                timestamp,
                miner: `0x${Math.random().toString(16).substr(2, 40)}`,
                parentHash: `0x${Math.random().toString(16).substr(2, 64)}`,
                size: Math.floor(Math.random() * 1500) + 800,
                difficulty: Math.floor(Math.random() * 1000000)
            });
        }

        setLatestBlocks(blocks);
        setNetworkInfo({
            chainId: '1337',
            networkType: 'development (simulation)',
            gasPrice: (18 + Math.random() * 5).toFixed(1),
            blockHeight: baseBlockNumber,
            peerCount: 0,
            isListening: false,
            difficulty: 1000000,
            totalDifficulty: *********
        });

        setBlockchainStats({
            totalTransactions: 1247,
            avgBlockTime: 12,
            totalAccounts: 10,
            totalSupply: '1000.00',
            networkHashrate: 847291,
            avgGasPrice: 20.5,
            blockSize: 1205
        });
    };

    // Fonction de recherche améliorée
    const handleSearch = async () => {
        if (!searchInput.trim()) return;

        try {
            setLoading(true);

            if (!web3) {
                alert("Web3 n'est pas connecté");
                return;
            }

            // Recherche de bloc par numéro
            if (!isNaN(searchInput)) {
                const blockNumber = parseInt(searchInput);
                const block = await web3.eth.getBlock(blockNumber, true);

                if (block) {
                    setBlockDetails(block);
                    setTransactionDetails(null);
                } else {
                    alert("Bloc non trouvé");
                }
            }
            // Recherche de transaction par hash
            else if (searchInput.startsWith('0x') && searchInput.length === 66) {
                try {
                    const [tx, receipt] = await Promise.all([
                        web3.eth.getTransaction(searchInput),
                        web3.eth.getTransactionReceipt(searchInput).catch(() => null)
                    ]);

                    if (tx) {
                        setTransactionDetails({ ...tx, receipt });
                        setBlockDetails(null);
                    } else {
                        alert("Transaction non trouvée");
                    }
                } catch (e) {
                    alert("Transaction non trouvée");
                }
            }
            // Recherche d'adresse
            else if (searchInput.startsWith('0x') && searchInput.length === 42) {
                try {
                    const [balance, transactionCount] = await Promise.all([
                        web3.eth.getBalance(searchInput),
                        web3.eth.getTransactionCount(searchInput)
                    ]);

                    alert(`Adresse: ${searchInput}\nSolde: ${web3.utils.fromWei(balance, 'ether')} ETH\nTransactions: ${transactionCount}`);
                } catch (e) {
                    alert("Erreur lors de la récupération des informations de l'adresse");
                }
            }
            // Recherche de bloc par hash
            else if (searchInput.startsWith('0x') && searchInput.length === 66) {
                const block = await web3.eth.getBlock(searchInput, true);

                if (block) {
                    setBlockDetails(block);
                    setTransactionDetails(null);
                } else {
                    alert("Bloc non trouvé");
                }
            } else {
                alert("Format invalide. Utilisez:\n- Numéro de bloc (ex: 123)\n- Hash de transaction/bloc (0x...)\n- Adresse (0x...)");
            }
        } catch (error) {
            console.error("Erreur lors de la recherche:", error);
            alert("Erreur lors de la recherche");
        } finally {
            setLoading(false);
        }
    };

    const formatTimeAgo = (timestamp) => {
        const now = Math.floor(Date.now() / 1000);
        const seconds = now - timestamp;

        if (seconds < 60) return `${seconds}s ago`;
        if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
        if (seconds < 86400) return `${Math.floor(seconds / 3600)}h ago`;
        return `${Math.floor(seconds / 86400)}d ago`;
    };

    const formatNumber = (num) => {
        return new Intl.NumberFormat().format(num);
    };

    const getBlockColor = (index) => {
        const colors = [
            'from-red-400 to-red-600',
            'from-orange-400 to-orange-600',
            'from-amber-400 to-amber-600',
            'from-yellow-400 to-yellow-600',
            'from-lime-400 to-lime-600',
            'from-green-400 to-green-600',
            'from-emerald-400 to-emerald-600',
            'from-teal-400 to-teal-600',
            'from-cyan-400 to-cyan-600',
            'from-sky-400 to-sky-600',
            'from-blue-400 to-blue-600',
            'from-indigo-400 to-indigo-600',
            'from-purple-400 to-purple-600',
            'from-pink-400 to-pink-600',
            'from-rose-400 to-rose-600'
        ];
        return colors[index % colors.length];
    };

    const renderConnectionStatus = () => (
        <div className={`flex items-center gap-2 px-4 py-3 rounded-full text-sm font-medium shadow-lg ${
            isConnected
                ? 'bg-green-100 text-green-800 border border-green-200'
                : 'bg-red-100 text-red-800 border border-red-200'
        }`}>
            {isConnected ? <Wifi className="w-4 h-4" /> : <WifiOff className="w-4 h-4" />}
            <span>{isConnected ? 'Connecté à Ganache' : 'Déconnecté'}</span>
            {lastBlockTime !== null && isConnected && (
                <span className="ml-2 px-2 py-1 bg-green-200 rounded-full text-xs">
                    Dernier bloc: {lastBlockTime}s
                </span>
            )}
        </div>
    );

    // Render des statistiques étendues
    const renderExtendedStats = () => (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                    <TrendingUp className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">Hauteur</span>
                </div>
                <p className="text-2xl font-bold text-blue-900">{formatNumber(networkInfo.blockHeight)}</p>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                    <Zap className="w-5 h-5 text-green-600" />
                    <span className="text-sm font-medium text-green-800">Gas Price</span>
                </div>
                <p className="text-xl font-bold text-green-900">
                    {parseFloat(networkInfo.gasPrice).toFixed(1)} Gwei
                </p>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl border border-purple-200">
                <div className="flex items-center gap-2 mb-2">
                    <Hash className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium text-purple-800">Chain ID</span>
                </div>
                <p className="text-2xl font-bold text-purple-900">{networkInfo.chainId}</p>
            </div>

            <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-xl border border-orange-200">
                <div className="flex items-center gap-2 mb-2">
                    <Timer className="w-5 h-5 text-orange-600" />
                    <span className="text-sm font-medium text-orange-800">Avg Block Time</span>
                </div>
                <p className="text-xl font-bold text-orange-900">{blockchainStats.avgBlockTime}s</p>
            </div>

            <div className="bg-gradient-to-br from-teal-50 to-teal-100 p-4 rounded-xl border border-teal-200">
                <div className="flex items-center gap-2 mb-2">
                    <Database className="w-5 h-5 text-teal-600" />
                    <span className="text-sm font-medium text-teal-800">Total TX</span>
                </div>
                <p className="text-xl font-bold text-teal-900">{formatNumber(blockchainStats.totalTransactions)}</p>
            </div>

            <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 p-4 rounded-xl border border-indigo-200">
                <div className="flex items-center gap-2 mb-2">
                    <Users className="w-5 h-5 text-indigo-600" />
                    <span className="text-sm font-medium text-indigo-800">Supply</span>
                </div>
                <p className="text-lg font-bold text-indigo-900">{blockchainStats.totalSupply} ETH</p>
            </div>
        </div>
    );

    // Render des transactions récentes
    const renderRecentTransactions = () => (
        <div className="bg-white rounded-2xl p-6 shadow-xl border border-gray-100 mb-8">
            <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-green-100 rounded-xl">
                    <Activity className="w-6 h-6 text-green-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800">Transactions Récentes</h2>
            </div>

            <div className="space-y-3">
                {recentTransactions.slice(0, 8).map((tx, index) => (
                    <div key={tx.hash} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 cursor-pointer transition-colors"
                         onClick={() => {
                             setSearchInput(tx.hash);
                             handleSearch();
                         }}>
                        <div className="flex items-center gap-4">
                            <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center text-white font-bold text-xs">
                                TX
                            </div>
                            <div>
                                <p className="font-mono text-sm text-blue-600">{tx.hash.substring(0, 20)}...</p>
                                <p className="text-xs text-gray-500">Bloc #{tx.blockNumber}</p>
                            </div>
                        </div>
                        <div className="text-right">
                            <p className="font-bold text-green-600">
                                {web3 ? parseFloat(web3.utils.fromWei(tx.value || '0', 'ether')).toFixed(4) : '0'} ETH
                            </p>
                            <p className="text-xs text-gray-500">
                                {formatTimeAgo(tx.timestamp)}
                            </p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );

    // Autres fonctions de rendu (blockDetails, transactionDetails) restent les mêmes...
    const renderBlockDetails = () => {
        if (!blockDetails) return null;

        return (
            <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 mb-8">
                <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 bg-blue-100 rounded-xl">
                        <Hash className="w-6 h-6 text-blue-600" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800">Bloc #{blockDetails.number}</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">Hash</span>
                            <span className="text-sm font-mono text-gray-800 break-all">{blockDetails.hash}</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">Hash Parent</span>
                            <span className="text-sm font-mono text-gray-800 break-all">{blockDetails.parentHash}</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">Mineur</span>
                            <span className="text-sm font-mono text-gray-800">{blockDetails.miner}</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">Timestamp</span>
                            <span className="text-sm text-gray-800">
                                {formatTimeAgo(blockDetails.timestamp)}
                                <br />
                                <span className="text-xs text-gray-500">
                                    {new Date(blockDetails.timestamp * 1000).toLocaleString()}
                                </span>
                            </span>
                        </div>
                    </div>

                    <div className="space-y-4">
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">Transactions</span>
                            <span className="text-lg font-bold text-green-600">{blockDetails.transactions.length}</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">Gas Utilisé</span>
                            <span className="text-sm text-gray-800">{formatNumber(blockDetails.gasUsed)}</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">Limite Gas</span>
                            <span className="text-sm text-gray-800">{formatNumber(blockDetails.gasLimit)}</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">Taille</span>
                            <span className="text-sm text-gray-800">{blockDetails.size ? formatNumber(blockDetails.size) : 'N/A'} bytes</span>
                        </div>
                    </div>
                </div>

                {/* Liste des transactions du bloc */}
                {blockDetails.transactions && blockDetails.transactions.length > 0 && (
                    <div className="mt-6">
                        <h4 className="text-lg font-semibold text-gray-800 mb-4">Transactions</h4>
                        <div className="space-y-2 max-h-64 overflow-y-auto">
                            {blockDetails.transactions.map((tx, index) => (
                                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer"
                                     onClick={() => {
                                         setSearchInput(tx.hash);
                                         handleSearch();
                                     }}>
                                    <span className="text-xs font-mono text-blue-600">{tx.hash}</span>
                                    <span className="text-sm text-gray-500">
                                        {web3 ? parseFloat(web3.utils.fromWei(tx.value || '0', 'ether')).toFixed(4) : '0'} ETH
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const renderTransactionDetails = () => {
        if (!transactionDetails) return null;

        return (
            <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 mb-8">
                <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 bg-green-100 rounded-xl">
                        <Activity className="w-6 h-6 text-green-600" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-800">Transaction</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">Hash</span>
                            <span className="text-sm font-mono text-gray-800 break-all">{transactionDetails.hash}</span>
                        </div>
                        <div className="flex justify-between items-center p-4 bg-gray-50 rounded-xl">
                            <span className="font-medium text-gray-600">De</span>
                            <span className="text-sm font-mono text-gray-800">{transactionDetails.from